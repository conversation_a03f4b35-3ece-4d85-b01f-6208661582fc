<template>
  <div class="extraction-task-list">
    <!-- 头部统计信息 -->
    <div v-if="extractionTasks.length > 0"
      class="mb-4 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
            <span class="text-sm font-medium text-amber-800 dark:text-amber-200">解压缩任务</span>
          </div>
          <div class="text-xs text-amber-600 dark:text-amber-400">
            总计: {{ extractionTasks.length }} |
            进行中: {{ activeExtractionTasks.length }} |
            已完成: {{ completedExtractionTasks.length }}
            <span v-if="errorExtractionTasks.length > 0" class="text-red-600 dark:text-red-400">
              | 失败: {{ errorExtractionTasks.length }}
            </span>
          </div>
        </div>

        <!-- 批量操作按钮 -->
        <div class="flex items-center space-x-2">
          <button v-if="activeExtractionTasks.length > 0" @click="pauseAllExtractions"
            class="px-3 py-1.5 text-xs font-medium text-amber-700 bg-amber-100 hover:bg-amber-200 dark:bg-amber-900 dark:text-amber-300 dark:hover:bg-amber-800 rounded-md transition-colors">
            全部暂停
          </button>

          <button v-if="pausedExtractionTasks.length > 0" @click="resumeAllExtractions"
            class="px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 rounded-md transition-colors">
            全部恢复
          </button>

          <button v-if="completedExtractionTasks.length > 0" @click="clearCompletedExtractions"
            class="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md transition-colors">
            清空已完成
          </button>
        </div>
      </div>
    </div>

    <!-- 解压缩任务列表 -->
    <div class="space-y-3">
      <ExtractionProgressCard v-for="task in extractionTasks" :key="task.id" :task="task"
        :show-password-dialog="passwordDialogs.has(task.id)" @pause="handlePauseExtraction"
        @resume="handleResumeExtraction" @cancel="handleCancelExtraction" @retry="handleRetryExtraction"
        @delete="handleDeleteExtraction" @password="handlePasswordSubmit" @password-cancel="handlePasswordCancel" />
    </div>

    <!-- 空状态 -->
    <div v-if="extractionTasks.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无解压缩任务</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        下载7z文件后会自动开始解压缩
      </p>
    </div>

    <!-- 密码输入对话框 -->
    <div v-if="currentPasswordTask" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click.self="cancelPasswordDialog">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400 mr-3" fill="none" stroke="currentColor"
            viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">输入解压密码</h3>
        </div>

        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          文件 "{{ currentPasswordTask.fileName }}" 需要密码才能解压缩
        </p>

        <div class="mb-4">
          <input v-model="passwordInput" type="password" placeholder="请输入解压密码"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            @keyup.enter="submitPasswordDialog" ref="passwordInputRef" />
        </div>

        <div class="flex justify-end space-x-3">
          <button @click="cancelPasswordDialog"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 rounded-md transition-colors">
            取消
          </button>
          <button @click="submitPasswordDialog" :disabled="!passwordInput.trim()"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors">
            确认
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick } from 'vue';
import { toast } from 'vue-sonner';
import ExtractionProgressCard from './ExtractionProgressCard.vue';
import { useStreamDownloadManager } from '@/composables/useStreamDownloadManager';
import type { DownloadTask } from '@/composables/useStreamDownloadManager';

const {
  tasks,
  pauseExtraction,
  resumeExtraction,
  cancelExtraction,
  startExtraction,
  removeTask,
} = useStreamDownloadManager();

// 响应式数据
const passwordDialogs = ref(new Set<string>());
const currentPasswordTask = ref<DownloadTask | null>(null);
const passwordInput = ref('');
const passwordInputRef = ref<HTMLInputElement>();

// 计算属性
const extractionTasks = computed(() =>
  tasks.value.filter(task => task.needsExtraction || task.extractionStatus)
);

const activeExtractionTasks = computed(() =>
  extractionTasks.value.filter(task => task.extractionStatus === 'extracting')
);

const pausedExtractionTasks = computed(() =>
  extractionTasks.value.filter(task => task.extractionStatus === 'paused')
);

const completedExtractionTasks = computed(() =>
  extractionTasks.value.filter(task => task.extractionStatus === 'completed')
);

const errorExtractionTasks = computed(() =>
  extractionTasks.value.filter(task => task.extractionStatus === 'error')
);

// 方法
const handlePauseExtraction = async (taskId: string) => {
  try {
    const success = await pauseExtraction(taskId);
    if (!success) {
      toast.error('暂停解压缩失败');
    }
  } catch (error) {
    console.error('暂停解压缩失败:', error);
    toast.error('暂停解压缩失败');
  }
};

const handleResumeExtraction = async (taskId: string) => {
  try {
    const success = await resumeExtraction(taskId);
    if (!success) {
      toast.error('恢复解压缩失败');
    }
  } catch (error) {
    console.error('恢复解压缩失败:', error);
    toast.error('恢复解压缩失败');
  }
};

const handleCancelExtraction = async (taskId: string) => {
  try {
    const success = await cancelExtraction(taskId);
    if (!success) {
      toast.error('取消解压缩失败');
    } else {
      toast.success('已取消解压缩');
    }
  } catch (error) {
    console.error('取消解压缩失败:', error);
    toast.error('取消解压缩失败');
  }
};

const handleRetryExtraction = async (taskId: string) => {
  try {
    const success = await startExtraction(taskId);
    if (!success) {
      toast.error('重试解压缩失败');
    }
  } catch (error) {
    console.error('重试解压缩失败:', error);
    toast.error('重试解压缩失败');
  }
};

const handleDeleteExtraction = async (taskId: string) => {
  try {
    await removeTask(taskId);
    toast.success('已删除解压缩任务');
  } catch (error) {
    console.error('删除解压缩任务失败:', error);
    toast.error('删除解压缩任务失败');
  }
};

const handlePasswordSubmit = (taskId: string, _password: string) => {
  // 这里应该调用解压缩管理器的密码提供方法
  // 暂时只是移除密码对话框
  passwordDialogs.value.delete(taskId);
  toast.info('密码已提交，正在重试解压缩...');
};

const handlePasswordCancel = (taskId: string) => {
  passwordDialogs.value.delete(taskId);
};

const pauseAllExtractions = async () => {
  const tasks = activeExtractionTasks.value;
  let successCount = 0;

  for (const task of tasks) {
    try {
      const success = await pauseExtraction(task.id);
      if (success) successCount++;
    } catch (error) {
      console.error(`暂停解压缩任务 ${task.fileName} 失败:`, error);
    }
  }

  if (successCount > 0) {
    toast.success(`已暂停 ${successCount} 个解压缩任务`);
  } else {
    toast.error('暂停解压缩任务失败');
  }
};

const resumeAllExtractions = async () => {
  const tasks = pausedExtractionTasks.value;
  let successCount = 0;

  for (const task of tasks) {
    try {
      const success = await resumeExtraction(task.id);
      if (success) successCount++;
    } catch (error) {
      console.error(`恢复解压缩任务 ${task.fileName} 失败:`, error);
    }
  }

  if (successCount > 0) {
    toast.success(`已恢复 ${successCount} 个解压缩任务`);
  } else {
    toast.error('恢复解压缩任务失败');
  }
};

const clearCompletedExtractions = async () => {
  const tasks = completedExtractionTasks.value;
  let successCount = 0;

  for (const task of tasks) {
    try {
      await removeTask(task.id);
      successCount++;
    } catch (error) {
      console.error(`删除解压缩任务 ${task.fileName} 失败:`, error);
    }
  }

  if (successCount > 0) {
    toast.success(`已清空 ${successCount} 个已完成的解压缩任务`);
  } else {
    toast.error('清空已完成任务失败');
  }
};

const showPasswordDialog = (_task: DownloadTask) => {
  currentPasswordTask.value = task;
  passwordInput.value = '';
  nextTick(() => {
    passwordInputRef.value?.focus();
  });
};

const submitPasswordDialog = () => {
  if (currentPasswordTask.value && passwordInput.value.trim()) {
    handlePasswordSubmit(currentPasswordTask.value.id, passwordInput.value.trim());
    currentPasswordTask.value = null;
    passwordInput.value = '';
  }
};

const cancelPasswordDialog = () => {
  if (currentPasswordTask.value) {
    handlePasswordCancel(currentPasswordTask.value.id);
    currentPasswordTask.value = null;
    passwordInput.value = '';
  }
};
</script>

<style scoped>
.extraction-task-list {
  /* 自定义样式 */
}
</style>
