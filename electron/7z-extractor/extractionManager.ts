import * as fs from "fs";
import * as path from "path";
// import * as os from "os";
import Store from "electron-store";
import { EventEmitter } from "events";
import { ChildProcess } from "child_process";
// import { pathTo7zip } from "7zip-bin"; // 7zip-bin模块导出有问题，使用动态导入
import { ExtractionWorkerManager } from "./extractionWorker";
import type { ExtractionTask, ExtractionConfig, ExtractionStatus, ExtractionStoreData } from "./types";

export class SevenZipExtractionManager extends EventEmitter {
  private store: Store<ExtractionStoreData>;
  private tasks: Map<string, ExtractionTask> = new Map();
  private config: ExtractionConfig;
  private activeExtractions: Map<string, ChildProcess> = new Map();
  private activeTasks: Set<string> = new Set();
  private workerManager: ExtractionWorkerManager;

  constructor(config: ExtractionConfig) {
    super();
    this.config = config;

    // 初始化存储
    this.store = new Store<ExtractionStoreData>({
      name: "7z-extractions",
      defaults: {
        tasks: {},
        settings: {
          maxConcurrent: config.maxConcurrent || 2,
          timeout: config.timeout || 5 * 60 * 1000, // 5分钟
          deleteOriginalAfterExtraction: config.deleteOriginalAfterExtraction || false,
          overwriteExisting: config.overwriteExisting || false,
          createSubfolder: config.createSubfolder !== false, // 默认true
          passwordPromptTimeout: config.passwordPromptTimeout || 30 * 1000, // 30秒
        },
      },
    });

    // 初始化Worker管理器
    this.workerManager = new ExtractionWorkerManager(config.maxConcurrent || 2);
    this.setupWorkerEventListeners();

    // 恢复未完成的任务
    this.restoreUnfinishedTasks();
  }

  /**
   * 创建解压缩任务
   */
  async createExtractionTask(
    archivePath: string,
    extractPath?: string,
    options?: {
      downloadTaskId?: string;
      deleteAfterExtraction?: boolean;
      password?: string;
    }
  ): Promise<string> {
    try {
      const taskId = this.generateTaskId();
      const fileName = path.basename(archivePath);

      // 检查文件是否存在
      if (!fs.existsSync(archivePath)) {
        throw new Error(`压缩文件不存在: ${archivePath}`);
      }

      // 获取文件大小
      const stats = fs.statSync(archivePath);
      const fileSize = stats.size;

      // 确定解压路径
      let finalExtractPath = extractPath;
      if (!finalExtractPath) {
        const dir = path.dirname(archivePath);
        const nameWithoutExt = path.basename(archivePath, path.extname(archivePath));
        finalExtractPath = path.join(dir, nameWithoutExt);
      }

      // 如果配置要求创建子文件夹，确保路径正确
      if (this.config.createSubfolder !== false) {
        const nameWithoutExt = path.basename(archivePath, path.extname(archivePath));
        if (!finalExtractPath.endsWith(nameWithoutExt)) {
          finalExtractPath = path.join(finalExtractPath, nameWithoutExt);
        }
      }

      // 确保目标目录存在
      await fs.promises.mkdir(finalExtractPath, { recursive: true });

      const task: ExtractionTask = {
        id: taskId,
        archivePath,
        extractPath: finalExtractPath,
        fileName,
        fileSize,
        extractedSize: 0,
        progress: 0,
        status: "pending",
        startTime: new Date(),
        speed: 0,
        remainingTime: 0,
        extractedFiles: 0,
        downloadTaskId: options?.downloadTaskId,
        deleteAfterExtraction: options?.deleteAfterExtraction ?? this.config.deleteOriginalAfterExtraction,
        password: options?.password,
      };

      this.tasks.set(taskId, task);
      this.saveTaskToStore(task);

      this.emit("task-created", taskId, task);
      console.log(`📦 创建解压缩任务: ${fileName} -> ${finalExtractPath}`);

      return taskId;
    } catch (error) {
      throw new Error(`无法创建解压缩任务: ${error}`);
    }
  }

  /**
   * 开始解压缩
   */
  async startExtraction(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`解压缩任务不存在: ${taskId}`);
    }

    if (task.status === "extracting") {
      return; // 已在解压中
    }

    // 检查并发限制
    if (this.activeTasks.size >= (this.config.maxConcurrent || 2)) {
      console.log(`⏳ 解压缩任务排队等待: ${task.fileName}`);
      return;
    }

    try {
      console.log(`🚀 开始解压缩: ${task.fileName}`);
      this.updateTaskStatus(taskId, "extracting");
      this.activeTasks.add(taskId);

      await this.performExtraction(taskId);
    } catch (error) {
      this.handleError(taskId, `启动解压缩失败: ${error}`);
    }
  }

  /**
   * 暂停解压缩
   */
  async pauseExtraction(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== "extracting") {
      return;
    }

    try {
      await this.workerManager.pauseExtraction(taskId);
      this.updateTaskStatus(taskId, "paused");
      this.activeTasks.delete(taskId);
      console.log(`⏸️ 暂停解压缩: ${task.fileName}`);

      // 尝试启动下一个排队的任务
      this.processQueue();
    } catch (error) {
      console.error(`暂停解压缩失败: ${task.fileName}`, error);
    }
  }

  /**
   * 恢复解压缩
   */
  async resumeExtraction(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== "paused") {
      return;
    }

    try {
      await this.workerManager.resumeExtraction(taskId);
      this.updateTaskStatus(taskId, "extracting");
      this.activeTasks.add(taskId);
      console.log(`▶️ 恢复解压缩: ${task.fileName}`);
    } catch (error) {
      console.error(`恢复解压缩失败: ${task.fileName}`, error);
      // 如果恢复失败，尝试重新开始
      await this.startExtraction(taskId);
    }
  }

  /**
   * 取消解压缩
   */
  async cancelExtraction(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    try {
      await this.workerManager.cancelExtraction(taskId);
      this.updateTaskStatus(taskId, "cancelled");
      this.activeTasks.delete(taskId);
      console.log(`❌ 取消解压缩: ${task.fileName}`);

      // 清理部分解压的文件
      try {
        if (fs.existsSync(task.extractPath)) {
          await fs.promises.rmdir(task.extractPath, { recursive: true });
        }
      } catch (error) {
        console.warn(`清理解压目录失败: ${error}`);
      }

      // 尝试启动下一个排队的任务
      this.processQueue();
    } catch (error) {
      console.error(`取消解压缩失败: ${task.fileName}`, error);
    }
  }

  /**
   * 删除解压缩任务
   */
  async deleteTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    // 如果任务正在进行中，先取消它
    if (task.status === "extracting") {
      await this.cancelExtraction(taskId);
    }

    this.cleanupTask(taskId);
    console.log(`🗑️ 删除解压缩任务: ${task.fileName}`);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): ExtractionTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取指定任务
   */
  getTask(taskId: string): ExtractionTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取活跃任务
   */
  getActiveTasks(): ExtractionTask[] {
    return Array.from(this.tasks.values()).filter((task) => ["pending", "extracting", "paused"].includes(task.status));
  }

  /**
   * 清理已完成的任务
   */
  clearCompletedTasks(): void {
    const completedTasks = Array.from(this.tasks.values()).filter((task) => ["completed", "error", "cancelled"].includes(task.status));

    console.log(`🧹 清空 ${completedTasks.length} 个已完成的解压缩任务`);
    completedTasks.forEach((task) => {
      this.cleanupTask(task.id);
    });
  }

  /**
   * 清空所有任务
   */
  async clearAllTasks(): Promise<void> {
    const allTasks = Array.from(this.tasks.values());
    console.log(`🧹 清空所有 ${allTasks.length} 个解压缩任务`);

    for (const task of allTasks) {
      try {
        if (["pending", "extracting", "paused"].includes(task.status)) {
          await this.cancelExtraction(task.id);
        } else {
          this.cleanupTask(task.id);
        }
      } catch (error) {
        console.error(`清空解压缩任务失败: ${task.fileName}`, error);
        this.cleanupTask(task.id);
      }
    }

    console.log(`✅ 已清空所有解压缩任务`);
  }

  /**
   * 关闭解压缩管理器
   */
  async shutdown(): Promise<void> {
    console.log("🧹 关闭解压缩管理器...");

    // 关闭所有Worker线程
    await this.workerManager.shutdown();

    // 清理所有任务
    this.tasks.clear();
    this.activeTasks.clear();
    this.activeExtractions.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    console.log("✅ 解压缩管理器已关闭");
  }

  // 私有方法

  private generateTaskId(): string {
    return `extract-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 设置Worker事件监听器
   */
  private setupWorkerEventListeners(): void {
    this.workerManager.on("task-progress", (taskId: string, progress: number, _extractedSize: number, _totalSize?: number) => {
      this.updateProgress(taskId, progress);
    });

    this.workerManager.on("task-completed", (taskId: string, _extractPath: string) => {
      this.handleSuccess(taskId);
    });

    this.workerManager.on("task-error", (taskId: string, error: string) => {
      this.handleError(taskId, error);
    });
  }

  private async performExtraction(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) return;

    try {
      console.log(`🔧 使用Worker线程执行解压缩: ${task.fileName}`);

      // 使用Worker管理器执行解压缩
      await this.workerManager.startExtraction(task);
    } catch (error) {
      this.handleError(taskId, `解压缩执行失败: ${error}`);
    }
  }

  // private setupProcessHandlers(taskId: string, process: ChildProcess): void {
  //   const task = this.tasks.get(taskId);
  //   if (!task) return;

  //   let outputBuffer = "";
  //   let errorBuffer = "";

  //   // 处理标准输出
  //   process.stdout?.on("data", (data: Buffer) => {
  //     outputBuffer += data.toString();
  //     this.parseSevenZipOutput(taskId, outputBuffer);
  //   });

  //   // 处理错误输出
  //   process.stderr?.on("data", (data: Buffer) => {
  //     errorBuffer += data.toString();
  //     console.warn(`7z stderr: ${data.toString()}`);
  //   });

  //   // 处理进程结束
  //   process.on("close", (code: number) => {
  //     this.activeExtractions.delete(taskId);
  //     this.activeTasks.delete(taskId);

  //     if (code === 0) {
  //       this.handleSuccess(taskId);
  //     } else {
  //       const error = errorBuffer || `解压缩失败，退出代码: ${code}`;
  //       this.handleError(taskId, error);
  //     }

  //     // 处理队列中的下一个任务
  //     this.processQueue();
  //   });

  //   // 处理进程错误
  //   process.on("error", (error: Error) => {
  //     this.activeExtractions.delete(taskId);
  //     this.activeTasks.delete(taskId);
  //     this.handleError(taskId, `进程错误: ${error.message}`);
  //     this.processQueue();
  //   });

  //   // 设置超时
  //   const timeout = this.config.timeout || 5 * 60 * 1000;
  //   setTimeout(() => {
  //     if (this.activeExtractions.has(taskId)) {
  //       process.kill("SIGKILL");
  //       this.handleError(taskId, "解压缩超时");
  //     }
  //   }, timeout);
  // }

  // private parseSevenZipOutput(taskId: string, output: string): void {
  //   const task = this.tasks.get(taskId);
  //   if (!task) return;

  //   // 解析7z输出，提取进度信息
  //   // 这里需要根据7z的实际输出格式来解析
  //   // 简化实现，实际需要更复杂的解析逻辑

  //   const lines = output.split("\n");
  //   for (const line of lines) {
  //     // 检查是否需要密码
  //     if (line.includes("Wrong password") || line.includes("Enter password")) {
  //       task.requiresPassword = true;
  //       this.emit("password-required", taskId, (password?: string) => {
  //         if (password) {
  //           task.password = password;
  //           // 重新启动解压缩
  //           this.startExtraction(taskId);
  //         } else {
  //           this.handleError(taskId, "需要密码但未提供");
  //         }
  //       });
  //       return;
  //     }

  //     // 解析进度信息（简化版本）
  //     const progressMatch = line.match(/(\d+)%/);
  //     if (progressMatch) {
  //       const progress = parseInt(progressMatch[1]);
  //       this.updateProgress(taskId, progress);
  //     }

  //     // 解析当前文件
  //     const fileMatch = line.match(/- (.+)/);
  //     if (fileMatch) {
  //       task.currentFile = fileMatch[1];
  //       task.extractedFiles++;
  //     }
  //   }
  // }

  private updateProgress(taskId: string, progress: number): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    const previousProgress = task.progress;
    task.progress = Math.min(100, Math.max(0, progress));

    // 计算解压速度和剩余时间
    const now = Date.now();
    const elapsed = now - task.startTime.getTime();

    if (elapsed > 0 && task.fileSize) {
      const extractedSize = (task.progress / 100) * task.fileSize;
      task.extractedSize = extractedSize;
      task.speed = extractedSize / (elapsed / 1000);

      if (task.speed > 0) {
        const remainingSize = task.fileSize - extractedSize;
        task.remainingTime = Math.round(remainingSize / task.speed);
      }
    }

    // 只在进度有显著变化时发出事件
    if (Math.abs(task.progress - previousProgress) >= 1) {
      this.emit("task-progress", taskId, task.progress, task.extractedSize, task.fileSize);
      this.saveTaskToStore(task);
    }
  }

  private handleSuccess(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.progress = 100;
    task.endTime = new Date();
    this.updateTaskStatus(taskId, "completed");
    this.emit("task-completed", taskId, task.extractPath);

    const duration = task.endTime.getTime() - task.startTime.getTime();
    console.log(`✅ 解压缩完成: ${task.fileName}, 耗时: ${Math.round(duration / 1000)}秒`);

    // 如果配置要求，删除原始文件
    if (task.deleteAfterExtraction) {
      this.deleteOriginalFile(task.archivePath);
    }
  }

  private handleError(taskId: string, error: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.error = error;
    task.endTime = new Date();
    this.updateTaskStatus(taskId, "error");
    this.emit("task-error", taskId, error);

    console.error(`❌ 解压缩失败: ${task.fileName} - ${error}`);
  }

  private async deleteOriginalFile(filePath: string): Promise<void> {
    try {
      await fs.promises.unlink(filePath);
      console.log(`🗑️ 已删除原始文件: ${filePath}`);
    } catch (error) {
      console.warn(`删除原始文件失败: ${error}`);
    }
  }

  private updateTaskStatus(taskId: string, status: ExtractionStatus): void {
    const task = this.tasks.get(taskId);
    if (!task) return;

    task.status = status;
    this.emit("task-status-changed", taskId, status, task.error);
    this.saveTaskToStore(task);
  }

  private saveTaskToStore(task: ExtractionTask): void {
    const tasks = this.store.get("tasks");
    tasks[task.id] = task;
    this.store.set("tasks", tasks);
  }

  private cleanupTask(taskId: string): void {
    this.tasks.delete(taskId);

    const tasks = this.store.get("tasks");
    delete tasks[taskId];
    this.store.set("tasks", tasks);
  }

  private restoreUnfinishedTasks(): void {
    const storedTasks = this.store.get("tasks");

    Object.values(storedTasks).forEach((task) => {
      if (!["completed", "error", "cancelled"].includes(task.status)) {
        // 重置为pending状态
        task.status = "pending";
        this.tasks.set(task.id, task);
      }
    });

    console.log(`📦 恢复了 ${this.tasks.size} 个未完成的解压缩任务`);
  }

  private processQueue(): void {
    // 检查是否有排队的任务可以启动
    const pendingTasks = Array.from(this.tasks.values()).filter((task) => task.status === "pending");

    for (const task of pendingTasks) {
      if (this.activeTasks.size < (this.config.maxConcurrent || 2)) {
        this.startExtraction(task.id);
      } else {
        break;
      }
    }
  }
}
